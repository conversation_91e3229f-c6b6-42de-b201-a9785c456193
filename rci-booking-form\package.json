{"name": "rci-platform", "homepage": "https://rci.transroute.com.mx/", "version": "0.1.0", "private": true, "devDependencies": {"@babel/core": "^7.16.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "eslint-plugin-react-hooks": "^4.6.0", "react-scripts": "5.0.1"}, "dependencies": {"@ashvin27/react-datatable": "^1.5.3", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "axios": "^0.22.0", "bootstrap": "^4.6.0", "crypto": "^1.0.1", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "foundation-sites": "^6.6.1", "fs": "^0.0.1-security", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "^16.14.0", "react-datepicker": "^4.21.0", "react-dom": "^16.14.0", "react-hook-form": "^7.47.0", "react-loading-overlay": "^1.0.1", "react-modal": "^3.11.2", "react-router-dom": "^5.1.2", "react-select": "^3.0.8", "react-time-picker": "^4.4.4", "reactstrap": "^8.9.0", "sass": "^1.54.3", "sass-loader": "^12.6.0", "slick-carousel": "^1.8.1", "style-loader": "^1.1.2", "sweetalert2": "^9.8.2", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browser": {"fs": false, "os": false, "path": false}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"@babel/core": "^7.16.0"}}