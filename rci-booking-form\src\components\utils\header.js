import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { handlelogout, getUserLvl } from "../../session/session_controller";

const Header = () => {
    const [userlvl] = useState(getUserLvl());

    const handleClickLogOut = () => {
        handlelogout();
    };

    return (
        <div id="top-bar" className="full align-self-bottom">
            <div className="grid-container">
                <div className="grid-x grid-padding-x grid-padding-y">
                    <a
                        className="small-12 medium-3 cell small-only-text-center  medium-text-left large-text-left"
                        href="/reserve">
                        <img
                            className="logo-header"
                            height="30"
                            src={process.env.PUBLIC_URL + "/logo-2020.svg"}
                            alt="RCI"
                        />
                    </a>
                    {/* el siguiente menu solo aparecerá en el formulario y las tablas */}
                    <div className="small-12 medium-9 cell align-self-middle text-right large-text-right">
                        <ul className="dropdown menu align-right" data-dropdown-menu>
                            <li>
                                <a href="/reserve">Booking Form</a>
                            </li>
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (<>
                            <li>
                                <a href="#">Reservations</a>
                                <ul class="menu">
                                    <li>
                                        <a href="/reservations">Call Center</a>
                                    </li>
                                    <li>
                                        <a href="/public-reservations">Public</a>
                                    </li>
                                </ul>
                            </li>
                            </>
                            )}
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (
                                <li>
                                    <a href="/fees">Fees</a>
                                </li>
                            )}
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (
                                <li>
                                    <a href="/fees-public">Fees Public</a>
                                </li>
                            )}
                            {userlvl === "3" ? (
                                <li></li>
                            ) : (
                                <li>
                                    <a href="/extra-service">Extra Services</a>
                                </li>
                            )}
                            {userlvl === "4" ? (
                                <li>
                                    <a href="/admin/error-logs" style={{color: '#ff6b6b'}}>
                                        ERROR LOGS
                                    </a>
                                </li>
                            ) : (
                                <li></li>
                            )}
                            <li>
                                <a href="/" onClick={handleClickLogOut}>
                                    <FontAwesomeIcon icon="sign-out-alt" /> Log out
                                </a>
                            </li>
                        </ul>
                    </div>
                    {/* fin del formulario */}
                </div>
            </div>
        </div>
    );
};

export default Header;
